// 消息状态管理 - Pinia Store
import { defineStore } from 'pinia'
import { ref, computed } from 'vue'
import { wsService, WebSocketState } from '../services/websocketService'
import type { TextMessage, SystemMessage } from '../services/websocketService'
import { apiClient } from '../api'
import { useWebSocketStore } from './websocket'
import { cacheService } from '../services/cacheService'

// 消息接口定义
export interface Message {
  id: string
  senderId: string
  receiverId: string
  content: string
  timestamp: number
  type: number
  isRead?: boolean
  isSending?: boolean
  sendError?: string
}

// 聊天会话接口
export interface ChatSession {
  userId: string
  userName: string
  userAvatar: string
  lastMessage?: Message
  unreadCount: number
  lastActiveTime: number
}

// 聊天历史响应接口
export interface ChatHistoryResponse {
  success: boolean
  messages: Message[]
  pagination: {
    page: number
    limit: number
    pages: number
  }
}

export const useMessageStore = defineStore('message', () => {
  // 获取全局 WebSocket 状态
  const webSocketStore = useWebSocketStore()

  // 状态 - 只保留必要的响应式状态
  const currentChatUserId = ref<string>('') // 当前聊天的用户ID
  const currentMessages = ref<Message[]>([]) // 当前聊天的消息列表
  const chatSessions = ref<ChatSession[]>([]) // 聊天会话列表
  const isLoading = ref(false)
  const error = ref<string>('')

  // 计算属性
  const sortedChatSessions = computed(() => {
    return [...chatSessions.value].sort((a, b) => b.lastActiveTime - a.lastActiveTime)
  })

  const totalUnreadCount = computed(() => {
    return chatSessions.value.reduce((total, session) => total + session.unreadCount, 0)
  })

  // 初始化WebSocket连接
  const initWebSocket = async (token?: string) => {
    try {
      console.log('MessageStore - 开始初始化WebSocket连接')

      // 设置WebSocket事件监听
      wsService.on('onStateChange', (state) => {
        // 状态变化时同步到全局 store
        webSocketStore.setState(state)
        console.log('MessageStore - WebSocket状态变更:', state)
      })

      wsService.on('onMessage', (message) => {
        handleIncomingMessage(message)
      })

      wsService.on('onSystemMessage', (message) => {
        handleIncomingSystemMessage(message)
      })

      wsService.on('onError', (errorMsg) => {
        error.value = errorMsg.message
        console.error('MessageStore - WebSocket错误:', errorMsg)
      })

      wsService.on('onHeartbeat', () => {
        console.log('MessageStore - 收到心跳响应')
      })

      // 在开始连接之前，确保状态同步
      console.log('MessageStore - 准备连接WebSocket')

      // 初始化缓存服务
      const currentUserId = getCurrentUserId()
      if (currentUserId) {
        try {
          await cacheService.initialize(currentUserId)
          console.log('MessageStore - 缓存服务初始化成功')

          // 加载聊天会话列表
          await loadChatSessions()
        } catch (cacheError) {
          console.error('MessageStore - 缓存服务初始化失败:', cacheError)
          // 缓存失败不影响 WebSocket 连接
        }
      }

      // 连接WebSocket
      await wsService.connect(token)
      console.log('MessageStore - WebSocket连接成功')

      // 延迟强制同步状态，确保组件能获取到正确的状态
      setTimeout(() => {
        console.log('MessageStore - 强制同步WebSocket状态')
        wsService.forceStateSync()
      }, 200)
    } catch (err) {
      console.error('WebSocket连接失败:', err)
      error.value = err instanceof Error ? err.message : '连接失败'
    }
  }

  // 处理接收到的消息
  const handleIncomingMessage = async (textMessage: TextMessage) => {
    console.log('🔍 [MessageStore] 开始处理接收到的消息:', textMessage)

    const currentUserId = getCurrentUserId()
    const chatUserId =
      textMessage.senderId === currentUserId ? textMessage.receiverId : textMessage.senderId

    // 如果用户正在当前聊天中，消息应该立即标记为已读
    const isCurrentChat = chatUserId === currentChatUserId.value
    const isReceivedMessage = textMessage.receiverId === currentUserId

    const message: Message = {
      id: textMessage.id,
      senderId: textMessage.senderId,
      receiverId: textMessage.receiverId,
      content: textMessage.content,
      timestamp: textMessage.timestamp,
      type: 1, // TEXT_MESSAGE
      isRead: isCurrentChat && isReceivedMessage // 如果是当前聊天且是接收的消息，立即标记为已读
    }

    console.log('🔍 [MessageStore] 当前用户ID:', currentUserId)
    console.log('🔍 [MessageStore] 消息发送者ID:', message.senderId)
    console.log('🔍 [MessageStore] 消息接收者ID:', message.receiverId)
    console.log('🔍 [MessageStore] 消息是否已读:', message.isRead)
    console.log('🔍 [MessageStore] 计算的聊天用户ID:', chatUserId)
    console.log('🔍 [MessageStore] 当前聊天用户ID:', currentChatUserId.value)

    // 如果是自己发送的消息，检查是否有对应的临时消息需要替换
    if (message.senderId === currentUserId && chatUserId === currentChatUserId.value) {
      console.log('🔍 [handleIncomingMessage] 这是自己发送的消息，检查是否需要替换临时消息')

      const tempMessageIndex = currentMessages.value.findIndex(
        (msg) =>
          msg.id.startsWith('temp_') && // 查找临时消息
          msg.content === message.content &&
          msg.receiverId === message.receiverId &&
          msg.senderId === message.senderId
      )

      if (tempMessageIndex !== -1) {
        console.log(
          '🔍 [handleIncomingMessage] ✅ 找到对应的临时消息，进行替换:',
          currentMessages.value[tempMessageIndex].id,
          '-> 服务器消息ID:',
          message.id
        )
        // 替换临时消息
        currentMessages.value[tempMessageIndex] = {
          ...message,
          isSending: false
        }

        // 保存到数据库
        try {
          await cacheService.cacheMessage(message, chatUserId)
        } catch (error) {
          console.error('🔍 [handleIncomingMessage] 缓存替换消息失败:', error)
        }

        // 更新聊天会话
        await updateChatSession(chatUserId, message)
        console.log('🔍 [handleIncomingMessage] 临时消息替换完成，直接返回')
        return
      } else {
        console.log('🔍 [handleIncomingMessage] ❌ 没有找到对应的临时消息，将作为新消息添加')
      }
    }

    // 添加消息到对应的聊天
    await addMessageToChat(chatUserId, message)

    // 更新聊天会话
    await updateChatSession(chatUserId, message)

    console.log('🔍 [MessageStore] 消息处理完成:', message)
  }

  // 处理接收到的系统消息
  const handleIncomingSystemMessage = (systemMessage: SystemMessage) => {
    console.log('收到系统消息:', systemMessage)
    // 暂时只记录日志
  }

  // 发送消息
  const sendMessage = async (receiverId: string, content: string): Promise<boolean> => {
    if (!content.trim()) {
      error.value = '消息内容不能为空'
      return false
    }

    if (!wsService.isConnected()) {
      error.value = 'WebSocket未连接'
      return false
    }

    // 创建临时消息对象
    const tempMessage: Message = {
      id: `temp_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      senderId: getCurrentUserId(),
      receiverId,
      content: content.trim(),
      timestamp: Date.now(),
      type: 1,
      isSending: true
    }

    // 立即添加到当前聊天显示（临时消息）
    if (receiverId === currentChatUserId.value) {
      currentMessages.value.push(tempMessage)
    }
    await updateChatSession(receiverId, tempMessage)

    try {
      // 发送消息
      const success = wsService.sendTextMessage(receiverId, content.trim())

      if (success) {
        // 更新消息状态为已发送
        await updateMessageStatus(receiverId, tempMessage.id, { isSending: false })
        return true
      } else {
        // 发送失败
        await updateMessageStatus(receiverId, tempMessage.id, {
          isSending: false,
          sendError: '发送失败'
        })
        error.value = '消息发送失败'
        return false
      }
    } catch (err) {
      console.error('发送消息失败:', err)
      await updateMessageStatus(receiverId, tempMessage.id, {
        isSending: false,
        sendError: '发送失败'
      })
      error.value = err instanceof Error ? err.message : '发送失败'
      return false
    }
  }

  // 获取聊天历史
  const loadChatHistory = async (otherUserId: string, page = 1, limit = 50): Promise<boolean> => {
    if (isLoading.value) return false

    isLoading.value = true
    error.value = ''

    try {
      const response = await apiClient.getChatHistory(otherUserId, page, limit)
      console.log(`API返回的聊天历史数据 (用户${otherUserId}):`, response)

      if (response.success) {
        console.log(
          `✅ 加载用户${otherUserId}的聊天历史 - 消息数量: ${response.messages?.length || 0}`
        )

        // 处理API返回的消息，转换时间戳格式
        const processedMessages = (response.messages || []).map((msg) => ({
          ...msg,
          timestamp:
            typeof msg.timestamp === 'string' ? new Date(msg.timestamp).getTime() : msg.timestamp
        }))

        console.log(`处理后的消息 (用户${otherUserId}):`, processedMessages)

        if (processedMessages.length > 0) {
          // 缓存新消息到本地数据库
          try {
            await cacheService.cacheMessages(processedMessages, otherUserId)
          } catch (error) {
            console.error('缓存历史消息失败:', error)
          }

          // 更新聊天会话
          const lastMessage = processedMessages[processedMessages.length - 1]
          if (lastMessage) {
            console.log(`更新聊天会话 (用户${otherUserId}), 最后消息:`, lastMessage)
            await updateChatSession(otherUserId, lastMessage)
          }
        } else {
          // 即使没有消息，也要确保创建聊天会话（避免显示"暂无消息"）
          console.log(`用户${otherUserId}没有新消息，检查是否需要创建空会话`)
          const existingSession = chatSessions.value.find((s) => s.userId === otherUserId)
          if (!existingSession) {
            console.log(`为用户${otherUserId}创建空的聊天会话`)
            // 创建一个空的聊天会话
            try {
              const userDetail = await apiClient.getUserDetail(otherUserId)
              if (userDetail.success) {
                const emptySession = {
                  userId: otherUserId,
                  userName: userDetail.user.displayName,
                  userAvatar: userDetail.user.avatar,
                  lastMessage: undefined,
                  unreadCount: 0,
                  lastActiveTime: Date.now()
                }
                chatSessions.value.push(emptySession)
                console.log(`空聊天会话已创建 - 用户${otherUserId}:`, emptySession)
              }
            } catch (err) {
              console.error('获取用户信息失败:', err)
            }
          } else {
            console.log(`用户${otherUserId}已有聊天会话，无需创建`)
          }
        }

        console.log(`加载${otherUserId}的聊天历史成功，共${processedMessages.length}条消息`)

        // 如果是当前聊天用户，刷新消息列表
        if (otherUserId === currentChatUserId.value) {
          await loadCurrentChatMessages()
        }
        return true
      } else {
        console.error(`获取聊天历史失败 - 用户${otherUserId}, API响应:`, response)
        error.value = '获取聊天历史失败'
        return false
      }
    } catch (err) {
      console.error('获取聊天历史失败:', err)
      error.value = err instanceof Error ? err.message : '获取聊天历史失败'
      return false
    } finally {
      isLoading.value = false
    }
  }

  // 添加消息到聊天（直接操作 IndexedDB）
  const addMessageToChat = async (userId: string, message: Message) => {
    console.log(
      '🔍 [addMessageToChat] ✅ 添加新消息:',
      message.id,
      message.content.substring(0, 20),
      '发送状态:',
      message.isSending ? '发送中' : '已发送',
      '错误状态:',
      message.sendError || '无'
    )

    // 直接保存到 IndexedDB
    if (!message.isSending) {
      // 只缓存已发送的消息
      try {
        await cacheService.cacheMessage(message, userId)
      } catch (error) {
        console.error('🔍 [addMessageToChat] 缓存消息失败:', error)
        // 缓存失败不影响正常流程
      }
    }

    // 如果是当前聊天用户，更新当前消息列表
    if (userId === currentChatUserId.value) {
      await loadCurrentChatMessages()
    }
  }

  // 加载当前聊天的消息列表
  const loadCurrentChatMessages = async (limit = 50, offset = 0) => {
    if (!currentChatUserId.value) {
      currentMessages.value = []
      return
    }

    try {
      const messages = await cacheService.getCachedMessages(currentChatUserId.value, limit, offset)
      currentMessages.value = messages
      console.log(`✅ [loadCurrentChatMessages] 加载消息: ${messages.length} 条`)
    } catch (error) {
      console.error('❌ [loadCurrentChatMessages] 加载消息失败:', error)
      currentMessages.value = []
    }
  }

  // 加载聊天会话列表
  const loadChatSessions = async () => {
    try {
      const sessions = await cacheService.getCachedChatSessions()
      chatSessions.value = sessions
      console.log(`✅ [loadChatSessions] 加载会话: ${sessions.length} 个`)
    } catch (error) {
      console.error('❌ [loadChatSessions] 加载会话失败:', error)
      chatSessions.value = []
    }
  }

  // 同步会话状态到数据库
  const syncChatSessionToDatabase = async (session: ChatSession) => {
    try {
      await cacheService.saveChatSession(session)
      console.log(`✅ [syncChatSessionToDatabase] 会话已同步: ${session.userId}`)
    } catch (error) {
      console.error('❌ [syncChatSessionToDatabase] 同步会话失败:', error)
    }
  }

  // 更新消息状态（在 IndexedDB 中更新）
  const updateMessageStatus = async (
    userId: string,
    messageId: string,
    updates: Partial<Message>
  ) => {
    console.log(`🔍 [updateMessageStatus] 更新消息状态:`, messageId, '更新内容:', updates)

    // 如果是当前聊天，更新本地显示
    if (userId === currentChatUserId.value) {
      const messageIndex = currentMessages.value.findIndex((msg) => msg.id === messageId)
      if (messageIndex !== -1) {
        Object.assign(currentMessages.value[messageIndex], updates)

        // 如果消息发送成功，保存到 IndexedDB
        if (updates.isSending === false && !updates.sendError) {
          try {
            await cacheService.cacheMessage(currentMessages.value[messageIndex], userId)
          } catch (error) {
            console.error('🔍 [updateMessageStatus] 缓存更新的消息失败:', error)
          }
        }
      }
    }
  }

  // 更新聊天会话
  const updateChatSession = async (userId: string, lastMessage: Message) => {
    console.log(`开始更新聊天会话 - 用户ID: ${userId}, 消息:`, lastMessage)
    let session = chatSessions.value.find((s) => s.userId === userId)
    const currentUserId = getCurrentUserId()

    // 判断是否是接收到的消息（需要增加未读计数）
    const isReceivedMessage =
      lastMessage.receiverId === currentUserId &&
      lastMessage.senderId === userId &&
      currentChatUserId.value !== userId // 不是当前聊天用户

    if (!session) {
      // 创建新的聊天会话，需要获取用户信息
      const initialUnreadCount = isReceivedMessage ? 1 : 0

      try {
        const userDetail = await apiClient.getUserDetail(userId)
        if (userDetail.success) {
          session = {
            userId,
            userName: userDetail.user.displayName,
            userAvatar: userDetail.user.avatar,
            lastMessage,
            unreadCount: initialUnreadCount,
            lastActiveTime: lastMessage.timestamp
          }
        } else {
          // 如果获取用户信息失败，使用默认信息
          session = {
            userId,
            userName: `用户${userId}`,
            userAvatar: '/avatars/default.png',
            lastMessage,
            unreadCount: initialUnreadCount,
            lastActiveTime: lastMessage.timestamp
          }
        }
      } catch (err) {
        console.error('获取用户信息失败:', err)
        session = {
          userId,
          userName: `用户${userId}`,
          userAvatar: '/avatars/default.png',
          lastMessage,
          unreadCount: initialUnreadCount,
          lastActiveTime: lastMessage.timestamp
        }
      }
    } else {
      // 更新现有会话
      session.lastMessage = lastMessage
      session.lastActiveTime = lastMessage.timestamp

      // 如果是接收到的消息且不是当前聊天用户，增加未读计数
      if (isReceivedMessage) {
        session.unreadCount = (session.unreadCount || 0) + 1
        console.log(`🔍 [updateChatSession] 增加未读计数: ${session.unreadCount}`)
      }
    }

    console.log(`🔍 [UpdateSession] 聊天会话信息已准备 - 用户ID: ${userId}`)

    // 同步会话到数据库
    await syncChatSessionToDatabase(session)

    // 重新加载会话列表，这样未读计数会根据数据库中的实际 isRead 状态重新计算
    await loadChatSessions()
  }

  // 设置当前聊天用户
  const setCurrentChatUser = async (userId: string) => {
    currentChatUserId.value = userId

    // 加载当前聊天的消息
    await loadCurrentChatMessages()

    // 将该聊天的所有消息标记为已读
    await markChatMessagesAsRead(userId)
  }

  // 将聊天消息标记为已读
  const markChatMessagesAsRead = async (chatUserId: string) => {
    try {
      // 获取该聊天的所有未读消息
      const messages = await cacheService.getCachedMessages(chatUserId, 1000, 0)
      const currentUserId = getCurrentUserId()

      // 找出需要标记为已读的消息（接收到的且未读的）
      const unreadMessages = messages.filter(
        (msg) => msg.receiverId === currentUserId && !msg.isRead
      )

      if (unreadMessages.length > 0) {
        // 标记为已读
        const updatedMessages = unreadMessages.map((msg) => ({
          ...msg,
          isRead: true
        }))

        // 批量更新到数据库
        await cacheService.cacheMessages(updatedMessages, chatUserId)

        // 更新会话的未读计数
        const existingSession = chatSessions.value.find((session) => session.userId === chatUserId)
        if (existingSession) {
          existingSession.unreadCount = 0
          // 同步更新后的会话到数据库
          await syncChatSessionToDatabase(existingSession)
        }

        // 重新加载会话列表以确保同步
        await loadChatSessions()

        console.log(`✅ [markChatMessagesAsRead] 已标记 ${unreadMessages.length} 条消息为已读`)
      }
    } catch (error) {
      console.error('❌ [markChatMessagesAsRead] 标记消息已读失败:', error)
    }
  }

  // 断开WebSocket连接
  const disconnectWebSocket = () => {
    wsService.disconnect()
    wsState.value = WebSocketState.DISCONNECTED

    // 关闭缓存服务
    cacheService.close()
  }

  // 手动重连WebSocket
  const manualReconnectWebSocket = () => {
    wsService.manualReconnect()
  }

  // 清除错误
  const clearError = () => {
    error.value = ''
  }

  // 获取当前用户ID
  const getCurrentUserId = (): string => {
    const user = apiClient.getCurrentUser()
    return user?.id || ''
  }

  // 重试发送失败的消息
  const retryMessage = async (userId: string, messageId: string): Promise<boolean> => {
    if (userId !== currentChatUserId.value) return false

    const message = currentMessages.value.find((msg) => msg.id === messageId)
    if (!message || !message.sendError) return false

    // 清除错误状态，设置为发送中
    await updateMessageStatus(userId, messageId, {
      sendError: undefined,
      isSending: true
    })

    // 重新发送
    const success = wsService.sendTextMessage(message.receiverId, message.content)

    if (success) {
      await updateMessageStatus(userId, messageId, { isSending: false })
      return true
    } else {
      await updateMessageStatus(userId, messageId, {
        isSending: false,
        sendError: '重发失败'
      })
      return false
    }
  }

  // 清理缓存
  const clearCache = async (): Promise<void> => {
    try {
      await cacheService.clearCache()
      console.log('✅ [MessageStore] 缓存已清理')
    } catch (error) {
      console.error('❌ [MessageStore] 清理缓存失败:', error)
    }
  }

  return {
    // 状态
    currentChatUserId,
    currentMessages,
    chatSessions,
    wsState: webSocketStore.state, // 使用全局 WebSocket 状态
    isLoading,
    error,

    // 计算属性
    sortedChatSessions,
    totalUnreadCount,

    // 方法
    initWebSocket,
    sendMessage,
    loadChatHistory,
    loadCurrentChatMessages,
    loadChatSessions,
    setCurrentChatUser,
    markChatMessagesAsRead,
    syncChatSessionToDatabase,
    disconnectWebSocket,
    manualReconnectWebSocket,
    clearError,
    retryMessage,
    clearCache,

    // 暴露 WebSocket store 的方法
    webSocketStore
  }
})
