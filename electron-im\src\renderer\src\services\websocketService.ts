// WebSocket 封装 (心跳, 重连, 消息处理)
import { API_CONFIG, APP_CONFIG } from '../config'
import { simpleim } from '../proto/message.js'
import { notificationService, ErrorType, ErrorSeverity } from './notificationService'

// 全局状态 store 引用（延迟导入避免循环依赖）
let webSocketStore: any = null

// 获取 WebSocket Store 实例
const getWebSocketStore = async () => {
  if (!webSocketStore) {
    // 延迟导入避免循环依赖
    const { useWebSocketStore } = await import('../store/websocket')
    webSocketStore = useWebSocketStore()
  }
  return webSocketStore
}

// 消息类型枚举
export enum MessageType {
  TEXT_MESSAGE = 1,
  SYSTEM_MESSAGE = 2,
  HEARTBEAT = 3
}

// 发送消息格式
export interface SendTextMessage {
  type: MessageType.TEXT_MESSAGE
  content: string
  receiverId: string
  timestamp: number
}

export interface SendHeartbeat {
  type: MessageType.HEARTBEAT
  timestamp: number
}

// 接收消息格式
export interface ReceivedTextMessage {
  type: MessageType.TEXT_MESSAGE
  messageId: string
  timestamp: number
  textMessage: {
    id: string
    senderId: string
    receiverId: string
    content: string
    timestamp: number
    type: number
  }
}

export interface ReceivedSystemMessage {
  type: MessageType.SYSTEM_MESSAGE
  messageId: string
  timestamp: number
  content: string
}

export interface ReceivedHeartbeat {
  type: MessageType.HEARTBEAT
  timestamp: number
}

// 联合类型
export type SendMessage = SendTextMessage | SendHeartbeat
export type ReceivedMessage = ReceivedTextMessage | ReceivedSystemMessage | ReceivedHeartbeat

// （用于事件回调）
export interface TextMessage {
  id: string
  senderId: string
  receiverId: string
  content: string
  timestamp: number
}

export interface SystemMessage {
  id: string
  content: string
  timestamp: number
}

export interface ErrorMessage {
  code: string
  message: string
}

// WebSocket 连接状态
export enum WebSocketState {
  CONNECTING = 'CONNECTING',
  CONNECTED = 'CONNECTED',
  DISCONNECTED = 'DISCONNECTED',
  RECONNECTING = 'RECONNECTING',
  ERROR = 'ERROR'
}

// WebSocket 事件类型
export interface WebSocketEvents {
  onStateChange: (state: WebSocketState) => void
  onMessage: (message: TextMessage) => void
  onSystemMessage: (message: SystemMessage) => void
  onError: (error: ErrorMessage) => void
  onHeartbeat: () => void
}

// WebSocket 服务类
export class WebSocketService {
  private ws: WebSocket | null = null
  private url: string
  private token: string | null = null
  private state: WebSocketState = WebSocketState.DISCONNECTED
  private heartbeatInterval: number | null = null
  private reconnectTimeout: number | null = null
  private reconnectAttempts = 0
  private maxReconnectAttempts = 5
  private heartbeatIntervalMs = 32000 // 29秒发送一次心跳
  private reconnectDelayMs = 3000 // 重连延迟
  private events: Partial<WebSocketEvents> = {}
  private connectionTimeout: number | null = null // 连接超时定时器
  private connectionTimeoutMs = 10000 // 连接超时时间 10秒
  private hasEverReconnected = false // 标记是否曾经重连过

  constructor(url?: string) {
    this.url = url || API_CONFIG.WS_URL
  }

  // 设置事件监听器
  on<K extends keyof WebSocketEvents>(event: K, callback: WebSocketEvents[K]): void {
    this.events[event] = callback
  }

  // 移除事件监听器
  off<K extends keyof WebSocketEvents>(event: K): void {
    delete this.events[event]
  }

  // 触发事件
  private emit<K extends keyof WebSocketEvents>(
    event: K,
    ...args: Parameters<WebSocketEvents[K]>
  ): void {
    const callback = this.events[event]
    if (callback) {
      ;(callback as any)(...args)
    }
  }

  // 设置状态
  private setState(newState: WebSocketState): void {
    if (this.state !== newState) {
      this.state = newState
      this.emit('onStateChange', newState)
      console.log(`WebSocket状态变更: ${newState}`)

      // 同步到全局状态
      this.syncToGlobalStore()
    }
  }

  // 同步状态到全局 Store
  private async syncToGlobalStore(): Promise<void> {
    try {
      const store = await getWebSocketStore()
      console.log('WebSocket - 同步状态到全局 Store:', {
        state: this.state,
        reconnectAttempts: this.reconnectAttempts,
        hasEverReconnected: this.hasEverReconnected
      })
      store.setState(this.state)
      store.setReconnectAttempts(this.reconnectAttempts)
      store.setHasEverReconnected(this.hasEverReconnected)
    } catch (error) {
      console.warn('WebSocket - 同步全局状态失败:', error)
    }
  }

  // 立即同步状态到全局 Store（同步版本）
  public syncToGlobalStoreSync(): void {
    // 使用 setTimeout 确保在下一个事件循环中执行
    setTimeout(() => {
      this.syncToGlobalStore()
    }, 0)
  }

  // 获取当前状态
  getState(): WebSocketState {
    return this.state
  }

  // 连接WebSocket
  connect(token?: string): Promise<void> {
    return new Promise((resolve, reject) => {
      try {
        // 如果已经连接，直接返回
        if (this.state === WebSocketState.CONNECTED) {
          resolve()
          return
        }

        // 设置token
        this.token = token || localStorage.getItem(APP_CONFIG.TOKEN_KEY)
        if (!this.token) {
          reject(new Error('未找到认证token'))
          return
        }

        // 如果这是首次连接（不是重连），重置重连次数
        if (this.state !== WebSocketState.RECONNECTING) {
          this.reconnectAttempts = 0
        }

        // 立即设置为连接中状态，确保组件能看到连接过程
        console.log('WebSocket - 开始连接，设置状态为 CONNECTING')
        this.setState(WebSocketState.CONNECTING)

        // 创建WebSocket连接，将token作为URL参数传递
        const wsUrl = `${this.url}?token=${this.token}`
        this.ws = new WebSocket(wsUrl)
        this.ws.binaryType = 'arraybuffer'

        // 设置连接超时
        this.connectionTimeout = window.setTimeout(() => {
          console.error('WebSocket连接超时')
          if (this.ws && this.ws.readyState === WebSocket.CONNECTING) {
            this.ws.close()
            this.setState(WebSocketState.ERROR)
            reject(new Error('连接超时'))
          }
        }, this.connectionTimeoutMs)

        // 连接成功
        this.ws.onopen = () => {
          console.log('WebSocket连接成功')

          // 清除连接超时定时器
          this.clearConnectionTimeout()

          // 先设置状态为已连接，让组件能获取到正确的重连次数
          this.setState(WebSocketState.CONNECTED)

          // 延迟重置重连次数，确保组件有时间获取到重连信息
          setTimeout(() => {
            console.log('WebSocket - 延迟重置重连次数，当前次数:', this.reconnectAttempts)
            this.reconnectAttempts = 0
            // 再次触发状态变化，确保组件能获取到重置后的状态
            this.emit('onStateChange', this.state)
          }, 100)

          this.startHeartbeat()

          resolve()
        }

        // 接收消息
        this.ws.onmessage = (event) => {
          this.handleMessage(event.data)
        }

        // 连接关闭
        this.ws.onclose = (event) => {
          console.log('WebSocket连接关闭:', event.code, event.reason)
          this.setState(WebSocketState.DISCONNECTED)
          this.stopHeartbeat()

          // 如果不是主动关闭，尝试重连
          if (event.code !== 1000) {
            this.attemptReconnect()
          }
        }

        // 连接错误
        this.ws.onerror = (error) => {
          console.error('WebSocket连接错误:', error)

          // 清除连接超时定时器
          this.clearConnectionTimeout()

          this.setState(WebSocketState.ERROR)

          reject(new Error('WebSocket连接失败'))
        }
      } catch (error) {
        console.error('创建WebSocket连接失败:', error)
        this.setState(WebSocketState.ERROR)
        reject(error)
      }
    })
  }

  // 断开连接
  disconnect(): void {
    console.log('主动断开WebSocket连接')
    this.stopHeartbeat()
    this.stopReconnect()
    this.clearConnectionTimeout()

    if (this.ws) {
      this.ws.close(1000, '用户主动断开')
      this.ws = null
    }

    this.setState(WebSocketState.DISCONNECTED)
  }

  // 处理接收到的消息 - 使用 Protobuf 解析
  private handleMessage(data: ArrayBuffer | string): void {
    try {
      let protoMessage: simpleim.IMMessage

      if (data instanceof ArrayBuffer) {
        // 二进制 Protobuf 数据
        const uint8Array = new Uint8Array(data)
        console.log('🔍 [WebSocket] 解码二进制 protobuf 数据，长度:', uint8Array.length)
        protoMessage = simpleim.IMMessage.decode(uint8Array)
      } else {
        // 如果是字符串，尝试解析为JSON
        console.log('🔍 [WebSocket] 收到字符串数据，尝试JSON解析:', data)
        const jsonMessage = JSON.parse(data)
        // 将JSON转换为Protobuf格式
        protoMessage = this.convertJsonToProto(jsonMessage)
      }

      console.log('🔍 [WebSocket] 解析后的 protobuf 消息:', protoMessage)

      // 根据消息类型处理
      switch (protoMessage.type) {
        case simpleim.MessageType.TEXT_MESSAGE: {
          if (protoMessage.textMessage) {
            const textMessage: TextMessage = {
              id: protoMessage.textMessage.id || '',
              senderId: protoMessage.textMessage.senderId || '',
              receiverId: protoMessage.textMessage.receiverId || '',
              content: protoMessage.textMessage.content || '',
              timestamp: Number(protoMessage.textMessage.timestamp) || Date.now()
            }
            console.log('🔍 [WebSocket] 解析后的文本消息:', textMessage)
            this.emit('onMessage', textMessage)
          }
          break
        }

        case simpleim.MessageType.ERROR: {
          if (protoMessage.errorMessage) {
            console.error('🔍 [WebSocket] 收到服务器错误:', protoMessage.errorMessage)
            this.emit('onError', {
              code: protoMessage.errorMessage.code || 'UNKNOWN_ERROR',
              message: protoMessage.errorMessage.message || '未知错误'
            })
          }
          break
        }

        case simpleim.MessageType.HEARTBEAT:
          console.log('🔍 [WebSocket] 收到心跳响应')
          this.emit('onHeartbeat')
          break

        default:
          console.warn('🔍 [WebSocket] 未知消息类型:', protoMessage.type)
      }
    } catch (error) {
      console.error('🔍 [WebSocket] 解析 protobuf 消息失败:', error)
      this.emit('onError', { code: 'PARSE_ERROR', message: '消息解析失败' })
    }
  }

  // 将JSON消息转换为Protobuf格式
  private convertJsonToProto(jsonMessage: any): simpleim.IMMessage {
    if (jsonMessage.type === 100) {
      // 错误消息
      return simpleim.IMMessage.create({
        type: simpleim.MessageType.ERROR,
        messageId: jsonMessage.messageId,
        timestamp: jsonMessage.timestamp,
        errorMessage: {
          code: jsonMessage.errorMessage?.code || 'UNKNOWN_ERROR',
          message: jsonMessage.errorMessage?.message || '未知错误'
        }
      })
    } else if (jsonMessage.type === 1) {
      // 文本消息
      return simpleim.IMMessage.create({
        type: simpleim.MessageType.TEXT_MESSAGE,
        messageId: jsonMessage.messageId,
        timestamp: jsonMessage.timestamp,
        textMessage: jsonMessage.textMessage || {
          id: jsonMessage.id,
          senderId: jsonMessage.senderId,
          receiverId: jsonMessage.receiverId,
          content: jsonMessage.content,
          timestamp: jsonMessage.timestamp
        }
      })
    } else {
      // 心跳或其他
      return simpleim.IMMessage.create({
        type: simpleim.MessageType.HEARTBEAT,
        timestamp: jsonMessage.timestamp
      })
    }
  }

  // 发送消息 - 使用 protobuf 二进制格式
  private sendMessage(message: any): boolean {
    if (this.state !== WebSocketState.CONNECTED || !this.ws) {
      console.error('WebSocket未连接，无法发送消息')
      return false
    }

    try {
      // 使用 protobuf 编码消息
      const protoMessage = simpleim.IMMessage.create(message)
      const buffer = simpleim.IMMessage.encode(protoMessage).finish()

      console.log('🔍 [WebSocket] 发送 protobuf 消息:', message)
      console.log('🔍 [WebSocket] 编码后的二进制长度:', buffer.length)

      this.ws.send(buffer)
      console.log('🔍 [WebSocket] protobuf 消息发送成功')
      return true
    } catch (error) {
      console.error('🔍 [WebSocket] 发送 protobuf 消息失败:', error)
      return false
    }
  }

  // 发送心跳
  private sendHeartbeat(): void {
    const heartbeatMessage = {
      type: simpleim.MessageType.HEARTBEAT,
      timestamp: Date.now(),
      token: this.token,
      heartbeat: {
        timestamp: Date.now(),
        status: 'online'
      }
    }

    console.log('🔍 [WebSocket] 准备发送心跳:', heartbeatMessage)
    this.sendMessage(heartbeatMessage)
  }

  // 发送文本消息
  sendTextMessage(receiverId: string, content: string): boolean {
    const currentUser = this.getCurrentUser()
    const messageId = this.generateMessageId()

    const textMessage = {
      type: simpleim.MessageType.TEXT_MESSAGE,
      messageId: messageId,
      timestamp: Date.now(),
      token: this.token,
      textMessage: {
        senderId: currentUser?.id || 'unknown',
        receiverId: receiverId,
        content: content
      }
    }

    console.log('🔍 [WebSocket] 准备发送文本消息 (protobuf 格式):', textMessage)
    return this.sendMessage(textMessage)
  }

  // 开始心跳
  private startHeartbeat(): void {
    this.stopHeartbeat()

    // 立即发送一次心跳
    this.sendHeartbeat()

    // 设置定时心跳
    this.heartbeatInterval = window.setInterval(() => {
      this.sendHeartbeat()
    }, this.heartbeatIntervalMs)

    console.log(`心跳已启动，间隔: ${this.heartbeatIntervalMs}ms`)
  }

  // 停止心跳
  private stopHeartbeat(): void {
    if (this.heartbeatInterval) {
      clearInterval(this.heartbeatInterval)
      this.heartbeatInterval = null
      console.log('心跳已停止')
    }
  }

  // 尝试重连
  private attemptReconnect(): void {
    if (this.reconnectAttempts >= this.maxReconnectAttempts) {
      console.error('重连次数已达上限，停止重连')
      this.setState(WebSocketState.ERROR)

      // 显示重连失败通知，提供手动重连选项
      this.showReconnectFailedNotification()
      return
    }

    this.reconnectAttempts++
    this.hasEverReconnected = true
    this.setState(WebSocketState.RECONNECTING)

    // 立即同步重连状态
    this.syncToGlobalStoreSync()

    console.log(`正在尝试第${this.reconnectAttempts}次重连...`)

    this.reconnectTimeout = window.setTimeout(() => {
      this.connect(this.token || undefined).catch((error) => {
        console.error(`第${this.reconnectAttempts}次重连失败:`, error)
        this.attemptReconnect()
      })
    }, this.reconnectDelayMs)
  }

  // 显示重连失败通知
  private showReconnectFailedNotification(): void {
    notificationService.handleError({
      type: ErrorType.WEBSOCKET,
      severity: ErrorSeverity.HIGH,
      message: '网络异常，无法自动重连，请检查网络或刷新页面',
      details: `已尝试重连 ${this.maxReconnectAttempts} 次，建议检查网络连接后手动重连`,
      retryable: true,
      action: () => this.manualReconnect(),
      actionText: '手动重连'
    })
  }

  // 手动重连
  public manualReconnect(): void {
    console.log('用户触发手动重连')

    // 重置重连计数，但标记为曾经重连过
    this.reconnectAttempts = 0
    this.hasEverReconnected = true

    // 显示手动重连提示
    notificationService.info('正在重新连接...')

    // 尝试连接
    this.connect(this.token || undefined).catch((error) => {
      console.error('手动重连失败:', error)
      notificationService.error('手动重连失败，请检查网络连接后重试')
    })
  }

  // 停止重连
  private stopReconnect(): void {
    if (this.reconnectTimeout) {
      clearTimeout(this.reconnectTimeout)
      this.reconnectTimeout = null
    }
    this.reconnectAttempts = 0
  }

  // 清除连接超时定时器
  private clearConnectionTimeout(): void {
    if (this.connectionTimeout) {
      clearTimeout(this.connectionTimeout)
      this.connectionTimeout = null
    }
  }

  // 生成消息ID
  private generateMessageId(): string {
    return `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
  }

  // 获取当前用户信息
  private getCurrentUser() {
    const userStr = localStorage.getItem(APP_CONFIG.USER_KEY)
    if (userStr) {
      try {
        return JSON.parse(userStr)
      } catch {
        return null
      }
    }
    return null
  }

  // 检查连接状态
  isConnected(): boolean {
    return this.state === WebSocketState.CONNECTED
  }

  // 获取重连次数
  getReconnectAttempts(): number {
    return this.reconnectAttempts
  }

  // 获取是否曾经重连过
  getHasEverReconnected(): boolean {
    return this.hasEverReconnected
  }

  // 强制触发状态同步（用于确保组件状态一致性）
  forceStateSync(): void {
    console.log('WebSocket - 强制触发状态同步:', {
      state: this.state,
      reconnectAttempts: this.reconnectAttempts,
      hasEverReconnected: this.hasEverReconnected
    })
    this.emit('onStateChange', this.state)
  }
}

// 导出WebSocket服务实例
export const wsService = new WebSocketService()
