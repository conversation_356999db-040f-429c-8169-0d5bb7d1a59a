// 测试未读消息计数功能的脚本
// 在浏览器控制台中运行此脚本来测试未读消息功能

console.log('🧪 开始测试未读消息计数功能...')

// 获取messageStore实例
const messageStore = window.__VUE_DEVTOOLS_GLOBAL_HOOK__?.apps?.[0]?.config?.globalProperties?.$messageStore

if (!messageStore) {
  console.error('❌ 无法获取messageStore实例')
} else {
  console.log('✅ 成功获取messageStore实例')
  
  // 模拟接收一条消息
  const testMessage = {
    id: `test_${Date.now()}`,
    senderId: 'test_user_123',
    receiverId: messageStore.getCurrentUserId(),
    content: '这是一条测试消息',
    timestamp: Date.now(),
    type: 1
  }
  
  console.log('🔍 模拟接收消息:', testMessage)
  
  // 调用消息处理函数
  messageStore.handleIncomingMessage(testMessage)
    .then(() => {
      console.log('✅ 消息处理完成')
      console.log('🔍 当前会话列表:', messageStore.chatSessions)
      
      // 检查未读计数
      const session = messageStore.chatSessions.find(s => s.userId === 'test_user_123')
      if (session) {
        console.log(`🔍 测试用户会话未读计数: ${session.unreadCount}`)
        if (session.unreadCount > 0) {
          console.log('✅ 未读消息计数正常增加')
        } else {
          console.log('❌ 未读消息计数没有增加')
        }
      } else {
        console.log('❌ 没有找到测试用户的会话')
      }
    })
    .catch(error => {
      console.error('❌ 消息处理失败:', error)
    })
}
