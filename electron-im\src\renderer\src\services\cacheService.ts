// 缓存服务 - 基于 IndexedDB 的单一数据源
import { dbManager } from '../db/db-manager'
import type { Message, ChatSession } from '../store/message'

/**
 * 缓存配置
 */
interface CacheConfig {
  enableEncryption: boolean // 是否启用加密
  defaultPageSize: number // 默认分页大小
}

/**
 * 缓存服务类
 * 基于 IndexedDB 的单一数据源，消除内存同步问题
 */
export class CacheService {
  private config: CacheConfig
  private isInitialized = false
  private currentUserId = ''

  constructor(config: Partial<CacheConfig> = {}) {
    this.config = {
      enableEncryption: true,
      defaultPageSize: 50,
      ...config
    }
  }

  /**
   * 初始化缓存服务
   */
  async initialize(userId: string): Promise<void> {
    if (this.isInitialized && this.currentUserId === userId) {
      return
    }

    try {
      this.currentUserId = userId

      // 初始化数据库管理器
      await dbManager.initialize(userId)

      this.isInitialized = true
      console.log('✅ [CacheService] 缓存服务初始化成功')
    } catch (error) {
      console.error('❌ [CacheService] 缓存服务初始化失败:', error)
      throw error
    }
  }

  /**
   * 缓存消息到本地数据库
   */
  async cacheMessage(message: Message, chatId: string): Promise<void> {
    if (!this.isInitialized) {
      console.warn('⚠️ [CacheService] 缓存服务未初始化，跳过消息缓存')
      return
    }

    try {
      await dbManager.saveMessage(message, chatId)
      console.log(`✅ [CacheService] 消息已缓存: ${message.id}`)
    } catch (error) {
      console.error('❌ [CacheService] 缓存消息失败:', error)
      // 不抛出错误，避免影响正常的消息流程
    }
  }

  /**
   * 批量缓存消息
   */
  async cacheMessages(messages: Message[], chatId: string): Promise<void> {
    if (!this.isInitialized || messages.length === 0) {
      return
    }

    try {
      await dbManager.saveMessages(messages, chatId)
      console.log(`✅ [CacheService] 批量缓存消息完成: ${messages.length} 条`)
    } catch (error) {
      console.error('❌ [CacheService] 批量缓存消息失败:', error)
    }
  }

  /**
   * 从缓存获取聊天消息
   */
  async getCachedMessages(chatId: string, limit = 50, offset = 0): Promise<Message[]> {
    if (!this.isInitialized) {
      return []
    }

    try {
      const messages = await dbManager.getChatMessages(chatId, limit, offset)
      console.log(`✅ [CacheService] 从缓存获取消息: ${messages.length} 条`)
      return messages
    } catch (error) {
      console.error('❌ [CacheService] 获取缓存消息失败:', error)
      return []
    }
  }

  /**
   * 获取最新的缓存消息
   */
  async getLatestCachedMessage(chatId: string): Promise<Message | null> {
    if (!this.isInitialized) {
      return null
    }

    try {
      return await dbManager.getLatestMessage(chatId)
    } catch (error) {
      console.error('❌ [CacheService] 获取最新缓存消息失败:', error)
      return null
    }
  }

  /**
   * 获取缓存的聊天会话（混合存储和动态生成）
   */
  async getCachedChatSessions(): Promise<ChatSession[]> {
    if (!this.isInitialized) {
      return []
    }

    try {
      const sessions = await dbManager.getMixedChatSessions()
      console.log(`✅ [CacheService] 从缓存获取聊天会话: ${sessions.length} 个`)
      return sessions
    } catch (error) {
      console.error('❌ [CacheService] 获取缓存聊天会话失败:', error)
      return []
    }
  }

  /**
   * 保存聊天会话到缓存
   */
  async saveChatSession(session: ChatSession): Promise<void> {
    if (!this.isInitialized) {
      console.warn('⚠️ [CacheService] 缓存服务未初始化，跳过会话保存')
      return
    }

    try {
      await dbManager.saveChatSession(session)
      console.log(`✅ [CacheService] 聊天会话已保存: ${session.userId}`)
    } catch (error) {
      console.error('❌ [CacheService] 保存聊天会话失败:', error)
      // 不抛出错误，避免影响正常流程
    }
  }

  /**
   * 删除聊天缓存
   */
  async deleteChatCache(chatId: string): Promise<void> {
    if (!this.isInitialized) {
      return
    }

    try {
      await dbManager.deleteChatMessages(chatId)
      console.log(`✅ [CacheService] 聊天缓存已删除: ${chatId}`)
    } catch (error) {
      console.error('❌ [CacheService] 删除聊天缓存失败:', error)
    }
  }

  /**
   * 清理缓存
   */
  async clearCache(): Promise<void> {
    if (!this.isInitialized) {
      return
    }

    try {
      await dbManager.clearUserData()
      console.log('✅ [CacheService] 缓存已清理')
    } catch (error) {
      console.error('❌ [CacheService] 清理缓存失败:', error)
    }
  }

  /**
   * 关闭缓存服务
   */
  close(): void {
    dbManager.close()
    this.isInitialized = false
    console.log('✅ [CacheService] 缓存服务已关闭')
  }

  /**
   * 获取缓存统计信息
   */
  async getCacheStats(): Promise<{
    totalMessages: number
    totalSessions: number
    databaseSize: string
  }> {
    // 这里可以实现缓存统计逻辑
    return {
      totalMessages: 0,
      totalSessions: 0,
      databaseSize: '未知'
    }
  }
}

// 导出缓存服务实例
export const cacheService = new CacheService()
